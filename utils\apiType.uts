export type LoginData= {
	username: string,
	password: string,
	clientId: string,
	grantType:string,
}

export type ApiResponse ={
  code: number;
  msg: string;
  data: any;
}

export type ProjectOption = { label: string; value: string | number };

// 修改密码
export type editPwd={
	phone?:number|string;
	yzm?:number|string;
	newPwd?:string,
	confirmPwd?:string,
	
}

// 选择下拉确认之后回传类型
export type confirmHcType={
	selectedItem?:ProjectOption;
	index?:number

}

// 工序分类数据类型
export type StepCategory = {
	stepCateId: string;
	cateName: string;
	createTime: string;
}

// 工序数据类型
export type Step = {
	stepId: string;
	stepName: string;
	stepCode: string;
	stepAlias: string | null;
	stepSort: number;
	cateIds: string;
	cateNames: string;
	createTime: string;
}

// 清单分类数据类型
export type InventoryCategory = {
	id: string;
	name: string;
	createTime: string;
}

// 清单数据类型
export type InventoryItem = {
	inventoryId: string;
	cateId: string;
	stepId: string;
	stepCateId: string;
	cateName: string;
	inventoryName: string;
	stepName: string;
	stepCateName: string;
}

// 清单分类组类型（第三级）
export type InventoryCategoryGroup = {
	cateId: string;
	cateName: string;
	items: InventoryItem[];
}

// 工序组类型（第二级）
export type StepGroup = {
	stepId: string;
	stepName: string;
	inventoryCategories: InventoryCategoryGroup[];
}

// 工序分类组类型（第一级）
export type StepCategoryGroup = {
	stepCateId: string;
	stepCateName: string;
	steps: StepGroup[];
}

// 临时分类组类型（用于数据分组处理）
export type TempCategoryGroup = {
	cateId: string;
	cateName: string;
	items: InventoryItem[];
}

// 临时工序组类型（用于数据分组处理）
export type TempStepGroup = {
	stepId: string;
	stepName: string;
	tempCategories: TempCategoryGroup[];
}

// 临时工序分类组类型（用于数据分组处理）
export type TempStepCategoryGroup = {
	stepCateId: string;
	stepCateName: string;
	tempSteps: TempStepGroup[];
}

// 注册提交信息
export type registerType={
	nickName:string|number,
	sex:string|number|null,
	workNum:string|number,
	tenantId:string|number,
	deptId:string|number,
	postId:string|number,
	phone?:number|string;
	yzm?:number|string;
	pwd?:string,
	confirmPwd?:string,
}

// 个人中心菜单
export type menuItemsType={
	label?:string,
	url?:string,
	permission?:string|number,
	id?:string|number
	
}

// 入职详情type
export type entryDeatilsType={
	title?:string,
	name?:string,
	time?:string,
	status?:string|number,
	rejectContent?:string,
	id?:string|number
	
}

// 个人信息type
// export type mineInfoType={
// 	nickName?:string,
// 	sex?:string|number,
// 	age?:string|number,
// 	phone?:string|number
// }


// 证书详情路由接收
export type zsOption =  {
	title:string,
	id?:number|string
}

// 证书新增 详情 修改 类型
export type zsInforType={
	name?:string,
	zsCode?:string|number,
	zsTime?:string,
	imgList?:string,
	zsYxq?:number|string
}

// 分页
export type  pageType={
	page:number,
	pageSize:number,
	total:number,
}
// 部门岗位用户数据类型
export type DeptPostUserItem = {
	id: string;
	name: string;
	type: string; // "1"=部门, "2"=岗位, "3"=用户
	parentId: number;
	isChild: string; // "0"=无下级, "1"=有下级
}

// 选择结果回调类型
export type DeptPostUserSelectResult = {
	id: string;
	name: string;
	type: string;
	path: string; // 完整路径，如"技术部 > 前端组 > 张三"
}

// 面包屑路径项类型
export type BreadcrumbItem = {
	id: string;
	name: string;
	type: string;
}

// 组织架构选择结果类型
export type OrgBosItem = {
	deptId: string; // 部门id
	postIds: Array<string>; // 岗位ids
	userIds: Array<string>; // 用户ids
}

// 最终选择结果类型
export type OrgBosResult = {
	orgBos: Array<OrgBosItem>;
}

// 展示用的数据项类型
export type DisplayItem = {
	id: string;           // 唯一标识
	path: string;         // 完整路径："技术部 > 前端开发 > 张三"
	type: string;         // 类型：dept, post, user
	originalId: string;   // 原始ID
}
// 考核数据
export type examineType={
	id?:number|string,
	daytitle?:string,
	dayScore?:number,
	showHide?:boolean,
	chlidren?:examinechlidrenType[],
	
}

// 考核数据chlidren
export type  examinechlidrenType={
	listTitle:string,
	listScore:number,
	id:number,
}

// 人事审批类型
export type personneplApprovalType={
	employmentApproval?:employmentApprovalType[]|null
	staffAdjust?:staffAdjustType[]|null
}

// 入职审批
export type employmentApprovalType={
	id?:number|string,
	nickName?:string,
	post?:string,
	dept?:string,
	time?:string,
}

// 员工跳转
export type staffAdjustType={
	id?:number|string,
	nickName?:string,
	post?:string,
	dept?:string,
	time?:string,
}

// 员工调整类型
export type ygtzType={
	nameId?:string|number,
	name?:string,
	status?:string,
	statusName?:string,
	remark?:string,
}

// 转办纪录tabs 类型
export type zbTabs={
	name?:string,
	id?:number
}