<template>
	<view class="status_bar">
		
	</view>
	<view class="nav-bar">
	    
	    <view class="nav-btn" @tap="handleBack">
	      <text class="iconfont icon_font">&#xe696;</text>
	    </view>
	    <text class="title_header">员工调整</text>
	
	    <view class="nav-btn"  ></view>
	</view>
	
	<scroll-view style="flex:1">
		<view class="page-container">
			<view class="info_header">
				<text class="iconfont rw_icon">&#xeafd;</text>
				<text class="title">任务详情</text>
			</view>
			<view class="form_div">
				<text class="form_label">转办人：</text>
				<text class="form_content">张某某</text>
			</view>
			<view class="form_div">
				<text class="form_label">任务名称：</text>
				<text class="form_content">土方工程填土任务</text>
			</view>
			<view class="form_div">
				<text class="form_label">任务编号：</text>
				<text class="form_content">031156987456</text>
			</view>
			<view class="form_div">
				<text class="form_label">工点：</text>
				<text class="form_content">路基50米处填上</text>
			</view>
			<view class="form_div">
				<text class="form_label">所属工序：</text>
				<text class="form_content">填土</text>
			</view>
			<view class="form_div">
				<text class="form_label">前置清单：</text>
				<text class="form_content">2025年2月12日清单</text>
			</view>
			<view class="form_div">
				<text class="form_label">创建人：</text>
				<text class="form_content">张世发</text>
			</view>
			<view class="form_div">
				<text class="form_label">创建时间：</text>
				<text class="form_content">2025/2/11 13:20:25</text>
			</view>
			<view class="form_div">
				<text class="form_label">任务周期：</text>
				<text class="form_content">2025/2/11 13:20:25-2025/2/11 13:20:25</text>
			</view>
			
			<view class="info_header">
				<text class="iconfont qdtx_icon">&#xe62b;</text>
				<text class="title">清单填写</text>
			</view>
			
			
			<textarea v-model="remark" :disabled="true" class="remark_textarea" placeholder="" ></textarea>
			
			<view class="bottom_view">
				<button @click="handleClick(1)" class="btn jujuejs">拒绝接收</button>
				<button @click="handleClick(2)" class="btn renwuzb">任务转办</button>
				<button @click="handleClick(3)" class="btn ribaosc">日报上传</button>
			</view>
		</view>
		
		<rejectPopup v-if="showReject" :show="showReject" :title="reject_title" @close="rejectClose" @confirm="rejectConfirm"></rejectPopup>
	</scroll-view>
	
</template>

<script setup>
	import rejectPopup from '@/components/reject_popup.uvue'
	
	const taskId=ref<string>('清单')
	
	// 备注
	const remark=ref<string>('')
	
	// 控制拒绝弹窗
	const showReject=ref<boolean>(false)
	// 弹窗标题
	const reject_title=ref<string>('拒绝接收的原因')
	
	
	// 按钮点击事件 1拒绝接收 2任务转办 3日报上传
	const handleClick=(i:number)=>{
		// console.log('i',i);
		if(i==1){
			console.log('拒绝接收');
			showReject.value=true
		}else if(i==2){
			console.log('任务转办');
		}else if(i==3){
			console.log('日报上传');
		}
	}
	
	// 拒绝接手取消
	const rejectClose=()=>{
		showReject.value=false
	}
	// 拒绝接受确认
	const rejectConfirm=(data:string)=>{
		console.log('拒绝接收',data);
		showReject.value=false
	}
	
	// 返回
	const handleBack=()=> {
	  uni.navigateBack()
	}

	
	onLoad((option: OnLoadOptions) => {
		// 安全获取属性值
		const id = option['id']
		if (id != null) {
		  taskId.value = id as string
		}
	})
</script>

<style lang="scss" scoped>
.page-container{
	background-color: #ffffff;
}
.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 10rpx;

  border-bottom-width: 1px;
  border-bottom-color: #EEEEEE; /* 可选分割线 */

}

/* 导航按钮容器 */
.nav-btn {
  height: 100%;
  padding: 0 16px;
  justify-content: center;
}
.xinzneg{
	width: 50rpx;
	height: 50rpx;
}

/* 返回图标 */
.icon {
  width: 36px;
  height: 36px;
}

/* 中间标题文字 */
.title_header {
  width: 50%;
  text-align: center;
  font-size: 36rpx;
  // font-weight: bold;
  color: #131313; /* 黑色文字 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_font{
	font-size: 32rpx;
	 color: #707070; /* 黑色文字 */
}
.info_header{
	width: 100%;
	background: #F7F8FA;
	padding: 30rpx 40rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	.title_icon{
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	.title{
		color: #131313;
		font-size: 32rpx;
		font-weight: bold;
	}
	.rw_icon{
		font-size: 50rpx;
		color: #58bbf6;
		margin-right: 10rpx;
	}
	.qdtx_icon{
		font-size: 36rpx;
		color: #58bbf6;
		margin-right: 10rpx;
	}
}
.form_div{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 40rpx 40rpx;
	border-bottom: 1rpx solid #e5e5e5;
	
	.form_label{
		color: #353535;
		font-size:30rpx;
	}
	.form_content{
		flex: 1;
		font-size: 28rpx;
		color: #656565;
		overflow: visible;
		text-align: right;
		// overflow: ;
		// text-overflow: ellipsis;
		// white-space: nowrap;
		
	}
	
}
.remark_textarea{
	width: 90%;
	margin: 0 auto;
	margin-top: 20rpx;
	padding: 20rpx 20rpx;
	background: #F3F3F3;
	font-size: 26rpx;
}
.bottom_view{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	width: 95%;
	margin: 20rpx auto;
	.btn{
		border-radius: 40rpx;
		font-size: 28rpx;
		width: 30%;
	}
	.jujuejs{
		background: #EFEFEF;
		border: 1rpx solid #EFEFEF;
	}
	.renwuzb{
		background: #EFEFEF;
		border: 1rpx solid #EFEFEF;
	}
	.ribaosc{
		background: #0189F6;
		border: 1rpx solid #0189F6;
		color: #ffffff;
	}
}

</style>
